import React, { useState } from 'react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';
import { useDashboard } from '../../contexts/DashboardContext';
import Card from '../common/Card';
import Button from '../common/Button';
import { Save } from 'lucide-react';

const PremiumAnalysisForm = () => {
  // Get dashboard context first
  const { selectedCustomerData, selectedPolicyData, setActiveTab, addScenario } = useDashboard();

  // Initialize form state for new scenarios
  const [formData, setFormData] = useState(() => {
    // Extract premium amount from policy data if available
    let initialPremium = '';
    if (selectedPolicyData) {
      // First try to get the premiumAmount field (numeric value from backend)
      if (selectedPolicyData.premiumAmount) {
        initialPremium = selectedPolicyData.premiumAmount.toString();
      }
      // Fall back to extracting from premium string if premiumAmount is not available
      else if (selectedPolicyData.premium) {
        const premiumMatch = selectedPolicyData.premium.match(/(\d+)/);
        initialPremium = premiumMatch ? premiumMatch[1] : '';
      }
    }

    // Calculate current age if customer data is available
    let initialAge = '40';
    if (selectedCustomerData?.details?.DOB) {
      const [day, month, year] = selectedCustomerData.details.DOB.split('.').map(Number);
      const birthDate = new Date(year, month - 1, day);
      const today = new Date();
      let age = today.getFullYear() - birthDate.getFullYear();
      const monthDiff = today.getMonth() - birthDate.getMonth();
      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
        age--;
      }
      initialAge = age.toString();
    }

    return {
      // Scenario 1: Annual Premium Modification
      currentPremium: initialPremium,
      changeToNewAmount: false,
      newPremiumAmount: '',
      modifyPremiumStartingAge: false,
      modifyPremiumAge: '',
      lumpSumPremium: false,
      lumpSumAmount: '',

      // Scenario 2: Premium Payment Cessation
      stopPremiumNow: false,
      stopPremiumAge: initialAge,
      modifyStopPremium: false,
      modifyStopPremiumAge: '',
      modifyStopPremiumPolicyYear: '',
      modifyStopPremiumCalendarYear: '',

      // Scenario 3: Cash Value Target Premium Calculation
      cashValueTarget: ''
    };
  });

  // State for modify premium by year functionality (similar to Face Amount page)
  const [modifyPremiumByYearData, setModifyPremiumByYearData] = useState({
    selectedTypes: {
      age: false,
      policyYear: false,
      calendarYear: false
    },
    ageRange: {
      start: 40, // Will be updated with actual current age
      end: 100
    },
    policyYearRange: {
      start: 1, // Will be updated with actual current policy year
      end: 100
    },
    calendarYearRange: {
      start: 2024, // Will be updated with actual current year
      end: 2100
    },
    isEditing: false,
    tableData: [] as any[]
  });

  // State for modify stop premium functionality (scenario 2)
  const [modifyStopPremiumData, setModifyStopPremiumData] = useState({
    selectedTypes: {
      age: false,
      policyYear: false,
      calendarYear: false
    },
    ageRange: {
      start: 40, // Will be updated with actual current age
      end: 100
    },
    policyYearRange: {
      start: 1, // Will be updated with actual current policy year
      end: 100
    },
    calendarYearRange: {
      start: 2024, // Will be updated with actual current year
      end: 2100
    }
  });

  // Analysis results state
  type ProjectionData = {
    year: number;
    basePremium: number;
    modifiedPremium: number;
    baseCashValue: number;
    modifiedCashValue: number;
  };
  const [analysisResults, setAnalysisResults] = useState<ProjectionData[] | null>(null);
  const [showReport, setShowReport] = useState(false);

  // Save scenario state
  const [isSaving, setIsSaving] = useState(false);

  // Handle form input changes
  const handleInputChange = (name: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle checkbox changes
  const handleCheckboxChange = (name: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      [name]: checked
    }));
  };

  // Handle option selection for scenario 1
  const handleScenario1Option = (option: string) => {
    setFormData(prev => ({
      ...prev,
      changeToNewAmount: option === 'changeAmount',
      modifyPremiumStartingAge: option === 'modifyAge',
      lumpSumPremium: option === 'lumpSum'
    }));
  };

  // Handle option selection for scenario 2
  const handleScenario2Option = (option: string) => {
    setFormData(prev => ({
      ...prev,
      stopPremiumNow: option === 'now',
      modifyStopPremium: option === 'modify'
    }));
  };

  // Helper functions for modify premium by year (similar to Face Amount page)
  const calculateCurrentAge = (): number => {
    if (!selectedCustomerData?.details?.DOB) return 40; // Default age

    const dob = selectedCustomerData.details.DOB;
    console.log('DOB from data:', dob); // Debug log

    // Handle different date formats
    let birthDate: Date;

    if (dob.includes('.')) {
      // Format: DD.MM.YYYY
      const [day, month, year] = dob.split('.').map(Number);
      birthDate = new Date(year, month - 1, day);
    } else if (dob.includes('/')) {
      // Format: MM/DD/YYYY or DD/MM/YYYY
      const parts = dob.split('/').map(Number);
      if (parts.length === 3) {
        // Assume MM/DD/YYYY format
        birthDate = new Date(parts[2], parts[0] - 1, parts[1]);
      } else {
        return 40; // Default if format is unclear
      }
    } else if (dob.includes('-')) {
      // Format: YYYY-MM-DD
      birthDate = new Date(dob);
    } else {
      return 40; // Default if format is unclear
    }

    const today = new Date();
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }

    console.log('Calculated age:', age); // Debug log
    return Math.max(0, age); // Ensure age is not negative
  };

  const getCurrentYear = (): number => {
    return new Date().getFullYear();
  };

  const calculateCurrentPolicyYear = (): number => {
    const issueDate = (selectedPolicyData as any)?.issueDate || (selectedPolicyData as any)?.policyStartDate;
    if (issueDate) {
      const issue = new Date(issueDate);
      const today = new Date();
      const yearsDiff = today.getFullYear() - issue.getFullYear();
      const monthsDiff = today.getMonth() - issue.getMonth();
      const daysDiff = today.getDate() - issue.getDate();

      // Calculate total months more accurately
      let totalMonths = yearsDiff * 12 + monthsDiff;
      if (daysDiff >= 0) {
        totalMonths += 1; // Add current month if we've passed the issue day
      }

      const policyYear = Math.max(1, Math.ceil(totalMonths / 12));
      return policyYear;
    }
    return 1; // Default if no issue date
  };

  // Initialize ranges when customer/policy data changes
  React.useEffect(() => {
    const currentAge = calculateCurrentAge();
    const currentYear = getCurrentYear();
    const currentPolicyYear = calculateCurrentPolicyYear();

    setModifyPremiumByYearData(prev => ({
      ...prev,
      ageRange: {
        start: currentAge,
        end: 100
      },
      policyYearRange: {
        start: currentPolicyYear,
        end: 100
      },
      calendarYearRange: {
        start: currentYear,
        end: 2100
      }
    }));

    // Initialize modifyStopPremiumData ranges
    setModifyStopPremiumData(prev => ({
      ...prev,
      ageRange: {
        start: currentAge,
        end: 100
      },
      policyYearRange: {
        start: currentPolicyYear,
        end: 100
      },
      calendarYearRange: {
        start: currentYear,
        end: 2100
      }
    }));
  }, [selectedCustomerData, selectedPolicyData]);

  // Generate table data based on selected types and ranges
  const generatePremiumTableData = () => {
    const { selectedTypes, ageRange, policyYearRange, calendarYearRange } = modifyPremiumByYearData;

    // Determine which range to use based on selected types
    let startYear = 0;
    let endYear = 0;

    if (selectedTypes.age) {
      const currentAge = calculateCurrentAge();
      const currentCalendarYear = getCurrentYear();
      startYear = currentCalendarYear + (ageRange.start - currentAge);
      endYear = currentCalendarYear + (ageRange.end - currentAge);
    } else if (selectedTypes.policyYear) {
      const currentPolicyYear = calculateCurrentPolicyYear();
      const currentCalendarYear = getCurrentYear();
      startYear = currentCalendarYear + (policyYearRange.start - currentPolicyYear);
      endYear = currentCalendarYear + (policyYearRange.end - currentPolicyYear);
    } else if (selectedTypes.calendarYear) {
      startYear = calendarYearRange.start;
      endYear = calendarYearRange.end;
    }

    if (startYear === 0 || endYear === 0 || startYear > endYear) return [];

    const currentAge = calculateCurrentAge();
    const currentCalendarYear = getCurrentYear();
    const currentPolicyYear = calculateCurrentPolicyYear();

    const data: any[] = [];

    // Limit to maximum 12 entries
    const maxEntries = 12;
    const totalYears = endYear - startYear + 1;
    const actualEndYear = totalYears > maxEntries ? startYear + maxEntries - 1 : endYear;

    for (let year = startYear; year <= actualEndYear; year++) {
      const row = {
        age: currentAge + (year - currentCalendarYear),
        policyYear: `Year ${currentPolicyYear + (year - currentCalendarYear)}`,
        calendarYear: year,
        premiumAmount: formData.currentPremium || '0' // Default premium amount
      };

      data.push(row);
    }

    return data;
  };

  // Update table data when selections change
  React.useEffect(() => {
    const newTableData = generatePremiumTableData();
    setModifyPremiumByYearData(prev => ({ ...prev, tableData: newTableData }));
  }, [modifyPremiumByYearData.selectedTypes, modifyPremiumByYearData.ageRange, modifyPremiumByYearData.policyYearRange, modifyPremiumByYearData.calendarYearRange, formData.currentPremium]);

  // Update current premium when policy data changes
  React.useEffect(() => {
    if (selectedPolicyData) {
      let newPremium = '';
      // First try to get the premiumAmount field (numeric value from backend)
      if (selectedPolicyData.premiumAmount) {
        newPremium = selectedPolicyData.premiumAmount.toString();
      }
      // Fall back to extracting from premium string if premiumAmount is not available
      else if (selectedPolicyData.premium) {
        const premiumMatch = selectedPolicyData.premium.match(/(\d+)/);
        newPremium = premiumMatch ? premiumMatch[1] : '';
      }

      // Update the form data with the new premium
      setFormData(prev => ({
        ...prev,
        currentPremium: newPremium
      }));
    }
  }, [selectedPolicyData]);

  // Generate sample projection data
  const generateProjectionData = () => {
    const years = Array.from({ length: 20 }, (_, i) => i + 1);
    const currentPremium = parseFloat(formData.currentPremium) || 5000;

    return years.map(year => {
      let basePremium = currentPremium;
      let modifiedPremium = currentPremium;
      let baseCashValue = currentPremium * year * 0.8;
      let modifiedCashValue = currentPremium * year * 0.8;

      // Apply new premium amount
      if (formData.changeToNewAmount && formData.newPremiumAmount) {
        const newPremium = parseFloat(formData.newPremiumAmount);
        modifiedPremium = newPremium;
        modifiedCashValue = baseCashValue + (newPremium - currentPremium) * year * 0.8;
      }

      // Apply modify premium starting from age
      if (formData.modifyPremiumStartingAge && formData.modifyPremiumAge && formData.newPremiumAmount) {
        const newPremium = parseFloat(formData.newPremiumAmount);
        const startAge = parseInt(formData.modifyPremiumAge) || 1;
        const currentAge = 40; // Assume current age
        const startYear = Math.max(1, startAge - currentAge + 1);

        if (year >= startYear) {
          modifiedPremium = newPremium;
          modifiedCashValue = baseCashValue + (newPremium - currentPremium) * (year - startYear + 1) * 0.8;
        }
      }

      // Apply lump sum premium
      if (formData.lumpSumPremium && formData.lumpSumAmount) {
        const lumpSum = parseFloat(formData.lumpSumAmount);
        if (year === 1) {
          modifiedPremium = lumpSum;
        } else {
          modifiedPremium = 0;
        }
        modifiedCashValue = lumpSum * Math.pow(1.04, year - 1);
      }

      // Apply stop premium payments
      if (formData.stopPremiumNow) {
        modifiedPremium = 0;
        modifiedCashValue = baseCashValue * 0.95; // Assume some decline without premiums
      } else if (formData.modifyStopPremium && formData.modifyStopPremiumAge) {
        const stopAge = parseInt(formData.modifyStopPremiumAge);
        const currentAge = 40; // Assume current age
        const stopYear = stopAge - currentAge + 1;
        if (year >= stopYear) {
          modifiedPremium = 0;
          modifiedCashValue = baseCashValue * 0.95; // Assume some decline without premiums
        }
      }

      // Apply cash value target adjustments
      if (formData.cashValueTarget) {
        const target = parseFloat(formData.cashValueTarget);
        const targetYear = 10; // Assume target in 10 years
        if (year <= targetYear) {
          const requiredGrowth = target / targetYear;
          modifiedCashValue = Math.max(modifiedCashValue, requiredGrowth * year);
        }
      }

      return {
        year,
        basePremium,
        modifiedPremium,
        baseCashValue,
        modifiedCashValue
      };
    });
  };

  // Run analysis
  const runAnalysis = () => {
    const projectionData = generateProjectionData();
    setAnalysisResults(projectionData);
  };

  // Generate report
  const generateReport = () => {
    runAnalysis();
    setShowReport(true);
  };

  // Save scenario to local storage and add to selected scenarios
  const saveScenario = async () => {
    if (!selectedCustomerData || !selectedPolicyData) {
      alert('Please select a customer and policy first!');
      return;
    }

    setIsSaving(true);
    try {
      // Create scenario name based on selected options
      let scenarioName = 'Premium Analysis';
      const scenarioDetails = [];

      if (formData.changeToNewAmount && formData.newPremiumAmount) {
        scenarioDetails.push(`New Premium: $${formData.newPremiumAmount}`);
      }
      if (formData.lumpSumPremium && formData.lumpSumAmount) {
        scenarioDetails.push(`Lump Sum: $${formData.lumpSumAmount}`);
      }
      if (formData.modifyPremiumStartingAge && formData.modifyPremiumAge) {
        scenarioDetails.push(`Modify from Age: ${formData.modifyPremiumAge}`);
      }
      if (formData.stopPremiumNow) {
        scenarioDetails.push('Stop Premium Now');
      }
      if (formData.modifyStopPremium && formData.modifyStopPremiumAge) {
        scenarioDetails.push(`Stop Premium at Age: ${formData.modifyStopPremiumAge}`);
      }
      if (formData.cashValueTarget) {
        scenarioDetails.push(`Cash Value Target: $${formData.cashValueTarget}`);
      }

      if (scenarioDetails.length > 0) {
        scenarioName += ` - ${scenarioDetails.join(', ')}`;
      }

      // Create the scenario object
      const newScenario = {
        id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
        name: scenarioName,
        policyId: selectedCustomerData.policyNumber || 'current-policy',
        asIsDetails: `Current Premium: $${extractedCustomerInfo?.annualPremium || formData.currentPremium || 'N/A'}`,
        whatIfOptions: scenarioDetails,
        category: 'premium' as const,
        keyPoints: [
          `Policy: ${selectedPolicyData.name}`,
          `Customer: ${selectedCustomerData.name}`,
          `Current Premium: $${extractedCustomerInfo?.annualPremium || formData.currentPremium || 'N/A'}`,
          ...scenarioDetails
        ],
        data: {
          formData,
          analysisResults,
          extractedCustomerInfo,
          timestamp: new Date().toISOString()
        },
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      // Add scenario to dashboard context (this will save to localStorage)
      await addScenario(newScenario);

      alert('Premium Analysis saved successfully and added to Selected Scenarios!');

      // Optionally reset form after save
      // resetForm();

    } catch (error) {
      console.error('Error saving scenario:', error);
      alert('Error saving scenario. Please try again.');
    } finally {
      setIsSaving(false);
    }
  };

  // Reset form
  const resetForm = () => {
    setFormData({
      // Scenario 1: Annual Premium Modification
      currentPremium: '',
      changeToNewAmount: false,
      newPremiumAmount: '',
      modifyPremiumStartingAge: false,
      modifyPremiumAge: '',
      lumpSumPremium: false,
      lumpSumAmount: '',

      // Scenario 2: Premium Payment Cessation
      stopPremiumNow: false,
      stopPremiumAge: '',
      modifyStopPremium: false,
      modifyStopPremiumAge: '',

      // Scenario 3: Cash Value Target Premium Calculation
      cashValueTarget: ''
    });
    setAnalysisResults(null);
    setShowReport(false);
  };

  // --- Customer Info Extraction (similar to AsIsPage) ---
  let extractedCustomerInfo = null;
  if (selectedCustomerData && selectedPolicyData) {
    // Extract premium amount - prioritize premiumAmount field, fall back to regex extraction
    let premiumAmount = '';
    if (selectedPolicyData.premiumAmount) {
      premiumAmount = selectedPolicyData.premiumAmount.toString();
    } else if (selectedPolicyData.premium) {
      const premiumMatch = selectedPolicyData.premium.match(/(\d+)/);
      premiumAmount = premiumMatch ? premiumMatch[1] : '';
    }

    // Extract coverage amount - prioritize faceAmount field, fall back to regex extraction
    let coverageAmount = '';
    if (selectedPolicyData.faceAmount) {
      coverageAmount = selectedPolicyData.faceAmount.toString();
    } else if (selectedPolicyData.coverage) {
      const coverageMatch = selectedPolicyData.coverage.replace(/,/g, '').match(/(\d+)/);
      coverageAmount = coverageMatch ? coverageMatch[1] : '';
    }
    // Calculate current age from DOB (assuming DD.MM.YYYY)
    const calculateAge = (dobString: string): string => {
      const [day, month, year] = dobString.split('.').map(Number);
      const birthDate = new Date(year, month - 1, day);
      const today = new Date();
      let age = today.getFullYear() - birthDate.getFullYear();
      const monthDiff = today.getMonth() - birthDate.getMonth();
      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
        age--;
      }
      return age.toString();
    };
    extractedCustomerInfo = {
      policyNumber: selectedCustomerData.details["Policy Number"] || selectedCustomerData.policyNumber,
      customerName: selectedCustomerData.name,
      customerId: selectedCustomerData.details["Customer ID"] || selectedCustomerData.customerId,
      policyType: selectedPolicyData.name,
      faceAmount: coverageAmount,
      annualPremium: premiumAmount,
      paymentPeriod: '20', // Default or extract if available
      dividendOption: 'Paid-up Additions', // Default or extract if available
      currentAge: calculateAge(selectedCustomerData.details.DOB),
      retirementAge: '65',
      lifeExpectancy: '85',
    };
  }

  // --- Render ---
  return (
    <div className="w-full max-w-none space-y-6 px-4 py-4">
      {/* Introduction Text */}
      <div className="bg-blue-50 p-6 rounded-lg border border-blue-200">
        <p className="text-lg text-gray-800 leading-relaxed">
          You may want to change the annual premium for making, or lower outgo, single payment, or to
          increase to accrue more cash value, shorten the payment period or pay catch-up premiums to restore
          or strengthen your policy's performance or model level premium. Your scenarios will be displayed for
          Current Interest Rate.
        </p>
      </div>





      {/* Show message if no policy is selected */}
      {(!selectedCustomerData || !selectedPolicyData) ? (
        <Card className="bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-yellow-100 dark:bg-yellow-900/40 rounded-full flex items-center justify-center">
              <span className="text-yellow-600 dark:text-yellow-400 text-sm">!</span>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-yellow-800 dark:text-yellow-200">No Policy Selected</h3>
              <p className="text-yellow-700 dark:text-yellow-300">
                Please go to the Policy Selection tab first to search and select a customer policy before configuring the Premium illustration.
              </p>
              <Button
                onClick={() => setActiveTab('policy-selection')}
                variant="outline"
                className="mt-3 border-yellow-300 text-yellow-700 hover:bg-yellow-100 dark:border-yellow-600 dark:text-yellow-300 dark:hover:bg-yellow-900/40"
              >
                Go to Policy Selection
              </Button>
            </div>
          </div>
        </Card>
      ) : (
        <div className="space-y-8">
          {/* Scenario 1: Annual Premium Modification */}
          <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-200">
            <h3 className="text-xl font-bold text-black mb-6 pb-3 border-b-2 border-gray-200">
              1. Your current annual premium is ?. Do you want to change it?
            </h3>

            {/* Small container for displaying current annual premium */}
            <div className="mb-6 w-fit bg-blue-50 border border-blue-200 rounded-lg px-5 py-3 flex items-center gap-2">
              <span className="font-semibold text-blue-900">Current Annual Premium:</span>
              <span className="text-lg font-bold text-blue-700">$
                {extractedCustomerInfo?.annualPremium || formData.currentPremium || '_______'}
              </span>
            </div>

            <div className="space-y-6">
              {/* Options */}
              <div className="space-y-4">
                {/* Option A: Change to New premium amount */}
                <div className="pl-4">
                  <label className="flex items-start space-x-3 text-lg font-semibold text-black">
                    <input
                      type="checkbox"
                      checked={formData.changeToNewAmount}
                      onChange={(e) => handleScenario1Option(e.target.checked ? 'changeAmount' : '')}
                      className="w-5 h-5 text-black mt-1"
                    />
                    <div className="flex-1">
                      <span>Change to New premium amount?</span>
                      {formData.changeToNewAmount && (
                        <div className="mt-3 bg-gray-50 p-4 rounded-lg border">
                          <div className="flex items-center space-x-2">
                            <span className="text-black">$</span>
                            <input
                              type="text"
                              value={formData.newPremiumAmount}
                              onChange={(e) => handleInputChange('newPremiumAmount', e.target.value)}
                              placeholder="Enter new premium amount"
                              className="w-48 px-3 py-2 border-2 border-gray-300 rounded-lg focus:border-blue-500 focus:outline-none bg-white text-black"
                            />
                          </div>
                        </div>
                      )}
                    </div>
                  </label>
                </div>

                {/* Option B: Lump Sum (One-time premium) now */}
                <div className="pl-4">
                  <label className="flex items-start space-x-3 text-lg font-semibold text-black">
                    <input
                      type="checkbox"
                      checked={formData.lumpSumPremium}
                      onChange={(e) => handleScenario1Option(e.target.checked ? 'lumpSum' : '')}
                      className="w-5 h-5 text-black mt-1"
                    />
                    <div className="flex-1">
                      <span>Lump Sum (One-time premium) now?</span>
                      {formData.lumpSumPremium && (
                        <div className="mt-3 bg-gray-50 p-4 rounded-lg border">
                          <div className="flex items-center space-x-2">
                            <span className="text-black">$</span>
                            <input
                              type="text"
                              value={formData.lumpSumAmount}
                              onChange={(e) => handleInputChange('lumpSumAmount', e.target.value)}
                              placeholder="Enter lump sum amount"
                              className="w-48 px-3 py-2 border-2 border-gray-300 rounded-lg focus:border-blue-500 focus:outline-none bg-white text-black"
                            />
                          </div>
                        </div>
                      )}
                    </div>
                  </label>
                </div>

                {/* Option C: Modify the premium starting from the age */}
                <div className="pl-4">
                  <label className="flex items-start space-x-3 text-lg font-semibold text-black">
                    <input
                      type="checkbox"
                      checked={formData.modifyPremiumStartingAge}
                      onChange={(e) => handleScenario1Option(e.target.checked ? 'modifyAge' : '')}
                      className="w-5 h-5 text-black mt-1"
                    />
                    <div className="flex-1">
                      <span>Modify the premium starting from the age?</span>
                      {formData.modifyPremiumStartingAge && (
                        <div className="mt-4 space-y-6">
                          {/* Single Container for Type Selection and Range Controls */}
                          <div className="bg-white p-6 rounded-lg border border-gray-300">
                            {/* Type Selection Checkboxes - only one can be selected */}
                            <div className="grid grid-cols-3 gap-4 mb-6">
                              <label className="flex items-center text-black font-semibold">
                                <input
                                  type="checkbox"
                                  checked={modifyPremiumByYearData.selectedTypes.age}
                                  onChange={(e) => setModifyPremiumByYearData(prev => ({
                                    ...prev,
                                    selectedTypes: {
                                      age: e.target.checked,
                                      policyYear: false,
                                      calendarYear: false
                                    }
                                  }))}
                                  className="mr-2"
                                />
                                Age
                              </label>
                              <label className="flex items-center text-black font-semibold">
                                <input
                                  type="checkbox"
                                  checked={modifyPremiumByYearData.selectedTypes.policyYear}
                                  onChange={(e) => setModifyPremiumByYearData(prev => ({
                                    ...prev,
                                    selectedTypes: {
                                      age: false,
                                      policyYear: e.target.checked,
                                      calendarYear: false
                                    }
                                  }))}
                                  className="mr-2"
                                />
                                Policy Year
                              </label>
                              <label className="flex items-center text-black font-semibold">
                                <input
                                  type="checkbox"
                                  checked={modifyPremiumByYearData.selectedTypes.calendarYear}
                                  onChange={(e) => setModifyPremiumByYearData(prev => ({
                                    ...prev,
                                    selectedTypes: {
                                      age: false,
                                      policyYear: false,
                                      calendarYear: e.target.checked
                                    }
                                  }))}
                                  className="mr-2"
                                />
                                Calendar Year
                              </label>
                            </div>

                            {/* Age Range Toggle Bars */}
                            {modifyPremiumByYearData.selectedTypes.age && (
                              <div className="space-y-4">
                                <div className="grid grid-cols-2 gap-6">
                                  <div>
                                    <label className="block text-sm font-bold text-black mb-2">Start Age</label>
                                    <div className="flex items-center space-x-3 bg-white border-2 border-gray-300 rounded-lg p-3">
                                      <button
                                        onClick={() => setModifyPremiumByYearData(prev => ({
                                          ...prev,
                                          ageRange: { ...prev.ageRange, start: Math.max(calculateCurrentAge(), prev.ageRange.start - 1) }
                                        }))}
                                        className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                      >
                                        ◀
                                      </button>
                                      <span className="text-xl font-bold text-black min-w-[3rem] text-center">
                                        {modifyPremiumByYearData.ageRange.start}
                                      </span>
                                      <button
                                        onClick={() => setModifyPremiumByYearData(prev => ({
                                          ...prev,
                                          ageRange: { ...prev.ageRange, start: Math.min(100, prev.ageRange.start + 1) }
                                        }))}
                                        className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                      >
                                        ▶
                                      </button>
                                    </div>
                                  </div>
                                  <div>
                                    <label className="block text-sm font-bold text-black mb-2">End Age</label>
                                    <div className="flex items-center space-x-3 bg-white border-2 border-gray-300 rounded-lg p-3">
                                      <button
                                        onClick={() => setModifyPremiumByYearData(prev => ({
                                          ...prev,
                                          ageRange: { ...prev.ageRange, end: Math.max(prev.ageRange.start, prev.ageRange.end - 1) }
                                        }))}
                                        className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                      >
                                        ◀
                                      </button>
                                      <span className="text-xl font-bold text-black min-w-[3rem] text-center">
                                        {modifyPremiumByYearData.ageRange.end}
                                      </span>
                                      <button
                                        onClick={() => setModifyPremiumByYearData(prev => ({
                                          ...prev,
                                          ageRange: { ...prev.ageRange, end: Math.min(100, prev.ageRange.end + 1) }
                                        }))}
                                        className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                      >
                                        ▶
                                      </button>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            )}

                            {/* Policy Year Range Toggle Bars */}
                            {modifyPremiumByYearData.selectedTypes.policyYear && (
                              <div className="space-y-4">
                                <div className="grid grid-cols-2 gap-6">
                                  <div>
                                    <label className="block text-sm font-bold text-black mb-2">Start Policy Year</label>
                                    <div className="flex items-center space-x-3 bg-white border-2 border-gray-300 rounded-lg p-3">
                                      <button
                                        onClick={() => setModifyPremiumByYearData(prev => ({
                                          ...prev,
                                          policyYearRange: { ...prev.policyYearRange, start: Math.max(calculateCurrentPolicyYear(), prev.policyYearRange.start - 1) }
                                        }))}
                                        className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                      >
                                        ◀
                                      </button>
                                      <span className="text-xl font-bold text-black min-w-[3rem] text-center">
                                        {modifyPremiumByYearData.policyYearRange.start}
                                      </span>
                                      <button
                                        onClick={() => setModifyPremiumByYearData(prev => ({
                                          ...prev,
                                          policyYearRange: { ...prev.policyYearRange, start: Math.min(100, prev.policyYearRange.start + 1) }
                                        }))}
                                        className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                      >
                                        ▶
                                      </button>
                                    </div>
                                  </div>
                                  <div>
                                    <label className="block text-sm font-bold text-black mb-2">End Policy Year</label>
                                    <div className="flex items-center space-x-3 bg-white border-2 border-gray-300 rounded-lg p-3">
                                      <button
                                        onClick={() => setModifyPremiumByYearData(prev => ({
                                          ...prev,
                                          policyYearRange: { ...prev.policyYearRange, end: Math.max(prev.policyYearRange.start, prev.policyYearRange.end - 1) }
                                        }))}
                                        className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                      >
                                        ◀
                                      </button>
                                      <span className="text-xl font-bold text-black min-w-[3rem] text-center">
                                        {modifyPremiumByYearData.policyYearRange.end}
                                      </span>
                                      <button
                                        onClick={() => setModifyPremiumByYearData(prev => ({
                                          ...prev,
                                          policyYearRange: { ...prev.policyYearRange, end: Math.min(100, prev.policyYearRange.end + 1) }
                                        }))}
                                        className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                      >
                                        ▶
                                      </button>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            )}

                            {/* Calendar Year Range Toggle Bars */}
                            {modifyPremiumByYearData.selectedTypes.calendarYear && (
                              <div className="space-y-4">
                                <div className="grid grid-cols-2 gap-6">
                                  <div>
                                    <label className="block text-sm font-bold text-black mb-2">Start Calendar Year</label>
                                    <div className="flex items-center space-x-3 bg-white border-2 border-gray-300 rounded-lg p-3">
                                      <button
                                        onClick={() => setModifyPremiumByYearData(prev => ({
                                          ...prev,
                                          calendarYearRange: { ...prev.calendarYearRange, start: Math.max(getCurrentYear(), prev.calendarYearRange.start - 1) }
                                        }))}
                                        className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                      >
                                        ◀
                                      </button>
                                      <span className="text-xl font-bold text-black min-w-[3rem] text-center">
                                        {modifyPremiumByYearData.calendarYearRange.start}
                                      </span>
                                      <button
                                        onClick={() => setModifyPremiumByYearData(prev => ({
                                          ...prev,
                                          calendarYearRange: { ...prev.calendarYearRange, start: Math.min(2100, prev.calendarYearRange.start + 1) }
                                        }))}
                                        className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                      >
                                        ▶
                                      </button>
                                    </div>
                                  </div>
                                  <div>
                                    <label className="block text-sm font-bold text-black mb-2">End Calendar Year</label>
                                    <div className="flex items-center space-x-3 bg-white border-2 border-gray-300 rounded-lg p-3">
                                      <button
                                        onClick={() => setModifyPremiumByYearData(prev => ({
                                          ...prev,
                                          calendarYearRange: { ...prev.calendarYearRange, end: Math.max(prev.calendarYearRange.start, prev.calendarYearRange.end - 1) }
                                        }))}
                                        className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                      >
                                        ◀
                                      </button>
                                      <span className="text-xl font-bold text-black min-w-[3rem] text-center">
                                        {modifyPremiumByYearData.calendarYearRange.end}
                                      </span>
                                      <button
                                        onClick={() => setModifyPremiumByYearData(prev => ({
                                          ...prev,
                                          calendarYearRange: { ...prev.calendarYearRange, end: Math.min(2100, prev.calendarYearRange.end + 1) }
                                        }))}
                                        className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                      >
                                        ▶
                                      </button>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            )}

                          </div>
                        </div>
                      )}
                    </div>
                  </label>
                </div>
              </div>

              {/* Buttons for Option B - outside container and above table */}
              {formData.modifyPremiumStartingAge && (
                <div>
                  <div className="flex justify-between items-center mt-6 mb-4">
                    <button
                      className="bg-blue-500 text-white px-4 py-2 rounded-lg font-semibold hover:bg-gray-600 transition-colors"
                    >
                      View Year by Year Details
                    </button>
                    <button
                      onClick={() => setModifyPremiumByYearData(prev => ({ ...prev, isEditing: !prev.isEditing }))}
                      className="bg-blue-500 text-white px-4 py-2 rounded-lg font-semibold hover:bg-blue-600 transition-colors"
                    >
                      {modifyPremiumByYearData.isEditing ? 'Lock Schedule' : 'Modify Schedule'}
                    </button>
                  </div>

                  {/* Table for Premium Schedule */}
                  {(modifyPremiumByYearData.selectedTypes.age || modifyPremiumByYearData.selectedTypes.policyYear || modifyPremiumByYearData.selectedTypes.calendarYear) && (
                    <div className="bg-white p-4 rounded-lg border border-gray-300">
                      <table className="w-full border-collapse">
                        <thead>
                          <tr className="bg-gray-100">
                            <th className="border border-gray-300 p-2 text-black font-semibold">Age</th>
                            <th className="border border-gray-300 p-2 text-black font-semibold">Policy Year</th>
                            <th className="border border-gray-300 p-2 text-black font-semibold">Calendar Year</th>
                            <th className="border border-gray-300 p-2 text-black font-semibold">Premium Amount</th>
                          </tr>
                        </thead>
                        <tbody>
                          {(() => {
                            // Determine the range based on selected type
                            let rows: { age: number; policyYear: number; calendarYear: number }[] = [];
                            if (modifyPremiumByYearData.selectedTypes.age) {
                              const { start, end } = modifyPremiumByYearData.ageRange;
                              for (let age = start; age <= end; age++) {
                                rows.push({
                                  age,
                                  policyYear: calculateCurrentPolicyYear() + (age - start),
                                  calendarYear: getCurrentYear() + (age - start),
                                });
                              }
                            } else if (modifyPremiumByYearData.selectedTypes.policyYear) {
                              const { start, end } = modifyPremiumByYearData.policyYearRange;
                              for (let py = start; py <= end; py++) {
                                rows.push({
                                  age: calculateCurrentAge() + (py - start),
                                  policyYear: py,
                                  calendarYear: getCurrentYear() + (py - start),
                                });
                              }
                            } else if (modifyPremiumByYearData.selectedTypes.calendarYear) {
                              const { start, end } = modifyPremiumByYearData.calendarYearRange;
                              for (let cy = start; cy <= end; cy++) {
                                rows.push({
                                  age: calculateCurrentAge() + (cy - start),
                                  policyYear: calculateCurrentPolicyYear() + (cy - start),
                                  calendarYear: cy,
                                });
                              }
                            } else {
                              // Default: show 12 rows from current age
                              const currentAge = calculateCurrentAge();
                              for (let i = 0; i < 12; i++) {
                                rows.push({
                                  age: currentAge + i,
                                  policyYear: calculateCurrentPolicyYear() + i,
                                  calendarYear: getCurrentYear() + i,
                                });
                              }
                            }

                            // Ensure tableData has enough rows
                            const tableData = [...modifyPremiumByYearData.tableData];
                            while (tableData.length < rows.length) {
                              tableData.push({ premiumAmount: '' });
                            }

                            return rows.map((rowInfo, index) => (
                              <tr key={index}>
                                <td className="border border-gray-300 p-2 text-center text-black">
                                  {rowInfo.age}
                                </td>
                                <td className="border border-gray-300 p-2 text-center text-black">
                                  Year {rowInfo.policyYear}
                                </td>
                                <td className="border border-gray-300 p-2 text-center text-black">
                                  {rowInfo.calendarYear}
                                </td>
                                <td className="border border-gray-300 p-2 text-center text-black">
                                  <input
                                    type="number"
                                    value={tableData[index]?.premiumAmount ?? ''}
                                    readOnly={!modifyPremiumByYearData.isEditing}
                                    onChange={(e) => {
                                      if (modifyPremiumByYearData.isEditing) {
                                        const newTableData = [...modifyPremiumByYearData.tableData];
                                        while (newTableData.length <= index) {
                                          newTableData.push({ premiumAmount: '' });
                                        }
                                        newTableData[index].premiumAmount = e.target.value;
                                        setModifyPremiumByYearData((prev: typeof modifyPremiumByYearData) => ({
                                          ...prev,
                                          tableData: newTableData,
                                        }));
                                      }
                                    }}
                                    className={`w-full p-2 border rounded ${
                                      modifyPremiumByYearData.isEditing
                                        ? 'border-blue-300 bg-white'
                                        : 'border-gray-300 bg-gray-100'
                                    }`}
                                    step="100"
                                  />
                                </td>
                              </tr>
                            ));
                          })()}
                        </tbody>
                      </table>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* Scenario 2: Premium Payment Cessation */}
          <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-200">
            <h3 className="text-xl font-bold text-black mb-6 pb-3 border-b-2 border-gray-200">
              2. Do you want to stop paying future premiums and see how long the policy remains in force? Or do you want to model policy lapse age/year by stopping premiums?
            </h3>

            <div className="space-y-6">
              <div className="space-y-4">
                {/* Option A: Now */}
                <div className="pl-4">
                  <label className="flex items-start space-x-3 text-lg font-semibold text-black">
                    <input
                      type="checkbox"
                      checked={formData.stopPremiumNow}
                      onChange={(e) => handleScenario2Option(e.target.checked ? 'now' : '')}
                      className="w-5 h-5 text-black mt-1"
                    />
                    <div className="flex-1">
                      <span>Stop Paying future premium now</span>
                    </div>
                  </label>
                </div>

                {/* Option B: Modify the */}
                <div className="pl-4">
                  <label className="flex items-start space-x-3 text-lg font-semibold text-black">
                    <input
                      type="checkbox"
                      checked={formData.modifyStopPremium}
                      onChange={(e) => handleScenario2Option(e.target.checked ? 'modify' : '')}
                      className="w-5 h-5 text-black mt-1"
                    />
                    <div className="flex-1">
                      <span>Modify stop paying future premium by year.</span>
                      {formData.modifyStopPremium && (
                        <div className="mt-3 bg-gray-50 p-4 rounded-lg border">
                          <div className="grid grid-cols-3 gap-4 mb-6">
                            <label className="flex items-center text-black font-semibold">
                              <input
                                type="checkbox"
                                checked={modifyStopPremiumData?.selectedTypes?.age}
                                onChange={(e) => setModifyStopPremiumData(prev => ({
                                  ...prev,
                                  selectedTypes: {
                                    age: e.target.checked,
                                    policyYear: false,
                                    calendarYear: false
                                  }
                                }))}
                                className="mr-2"
                              />
                              Age
                            </label>
                            <label className="flex items-center text-black font-semibold">
                              <input
                                type="checkbox"
                                checked={modifyStopPremiumData?.selectedTypes?.policyYear}
                                onChange={(e) => setModifyStopPremiumData(prev => ({
                                  ...prev,
                                  selectedTypes: {
                                    age: false,
                                    policyYear: e.target.checked,
                                    calendarYear: false
                                  }
                                }))}
                                className="mr-2"
                              />
                              Policy Year
                            </label>
                            <label className="flex items-center text-black font-semibold">
                              <input
                                type="checkbox"
                                checked={modifyStopPremiumData?.selectedTypes?.calendarYear}
                                onChange={(e) => setModifyStopPremiumData(prev => ({
                                  ...prev,
                                  selectedTypes: {
                                    age: false,
                                    policyYear: false,
                                    calendarYear: e.target.checked
                                  }
                                }))}
                                className="mr-2"
                              />
                              Calendar Year
                            </label>
                          </div>

                          {/* Age Range Toggle Bar */}
                          {modifyStopPremiumData?.selectedTypes?.age && (
                            <div className="space-y-4">
                              <div>
                                <label className="block text-sm font-bold text-black mb-2">Start Age</label>
                                <div className="flex items-center space-x-3 bg-white border-2 border-gray-300 rounded-lg p-3">
                                  <button
                                    onClick={() => setModifyStopPremiumData(prev => ({
                                      ...prev,
                                      ageRange: { ...prev.ageRange, start: Math.max(calculateCurrentAge(), prev.ageRange.start - 1) }
                                    }))}
                                    className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                  >
                                    ◀
                                  </button>
                                  <span className="text-xl font-bold text-black min-w-[3rem] text-center">
                                    {modifyStopPremiumData.ageRange.start}
                                  </span>
                                  <button
                                    onClick={() => setModifyStopPremiumData(prev => ({
                                      ...prev,
                                      ageRange: { ...prev.ageRange, start: Math.min(100, prev.ageRange.start + 1) }
                                    }))}
                                    className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                  >
                                    ▶
                                  </button>
                                </div>
                              </div>
                            </div>
                          )}

                          {/* Policy Year Range Toggle Bar */}
                          {modifyStopPremiumData?.selectedTypes?.policyYear && (
                            <div className="space-y-4">
                              <div>
                                <label className="block text-sm font-bold text-black mb-2">Start Policy Year</label>
                                <div className="flex items-center space-x-3 bg-white border-2 border-gray-300 rounded-lg p-3">
                                  <button
                                    onClick={() => setModifyStopPremiumData(prev => ({
                                      ...prev,
                                      policyYearRange: { ...prev.policyYearRange, start: Math.max(calculateCurrentPolicyYear(), prev.policyYearRange.start - 1) }
                                    }))}
                                    className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                  >
                                    ◀
                                  </button>
                                  <span className="text-xl font-bold text-black min-w-[3rem] text-center">
                                    {modifyStopPremiumData.policyYearRange.start}
                                  </span>
                                  <button
                                    onClick={() => setModifyStopPremiumData(prev => ({
                                      ...prev,
                                      policyYearRange: { ...prev.policyYearRange, start: Math.min(100, prev.policyYearRange.start + 1) }
                                    }))}
                                    className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                  >
                                    ▶
                                  </button>
                                </div>
                              </div>
                            </div>
                          )}

                          {/* Calendar Year Range Toggle Bar */}
                          {modifyStopPremiumData?.selectedTypes?.calendarYear && (
                            <div className="space-y-4">
                              <div>
                                <label className="block text-sm font-bold text-black mb-2">Start Calendar Year</label>
                                <div className="flex items-center space-x-3 bg-white border-2 border-gray-300 rounded-lg p-3">
                                  <button
                                    onClick={() => setModifyStopPremiumData(prev => ({
                                      ...prev,
                                      calendarYearRange: { ...prev.calendarYearRange, start: Math.max(getCurrentYear(), prev.calendarYearRange.start - 1) }
                                    }))}
                                    className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                  >
                                    ◀
                                  </button>
                                  <span className="text-xl font-bold text-black min-w-[3rem] text-center">
                                    {modifyStopPremiumData.calendarYearRange.start}
                                  </span>
                                  <button
                                    onClick={() => setModifyStopPremiumData(prev => ({
                                      ...prev,
                                      calendarYearRange: { ...prev.calendarYearRange, start: Math.min(2100, prev.calendarYearRange.start + 1) }
                                    }))}
                                    className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                  >
                                    ▶
                                  </button>
                                </div>
                              </div>
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  </label>
                </div>
              </div>
            </div>
          </div>

          {/* Scenario 3: Cash Value Target Premium Calculation */}
          <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-200">
            <h3 className="text-xl font-bold text-black mb-6 pb-3 border-b-2 border-gray-200">
              3. Calculate the new premium schedule, for a cash value target of ______ from now.
            </h3>

            <div className="space-y-6">
              <div className="space-y-4">
                <div className="pl-4">
                  <div className="bg-gray-50 p-4 rounded-lg border">
                    <div className="flex items-center space-x-2">
                      <span className="text-black font-semibold">Cash Value Target: $</span>
                      <input
                        type="text"
                        value={formData.cashValueTarget}
                        onChange={(e) => handleInputChange('cashValueTarget', e.target.value)}
                        placeholder="Enter target amount"
                        className="w-48 px-3 py-2 border-2 border-gray-300 rounded-lg focus:border-blue-500 focus:outline-none bg-white text-black"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-wrap gap-4 justify-center mt-8">
            <Button
              onClick={saveScenario}
              variant="primary"
              loading={isSaving}
              disabled={isSaving}
              className="flex items-center space-x-2 bg-indigo-600 hover:bg-indigo-700 text-white shadow-lg border-none"
            >
              <Save className="w-4 h-4" />
              <span>Save Premium Analysis</span>
            </Button>
            <Button
              onClick={resetForm}
              variant="primary"
              className="flex items-center space-x-2 bg-gray-600 hover:bg-gray-700 text-white shadow-lg border-none"
            >
              <span>Reset</span>
            </Button>
          </div>
        </div>
      )}

      {/* Analysis Results */}
      {analysisResults && (
        <div className="mt-8 bg-gradient-to-r from-green-50 to-green-100 p-6 rounded-xl shadow-lg border-l-4 border-green-500">
          <h3 className="text-xl font-bold text-green-800 mb-6 text-center">📊 Premium Analysis Results</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {/* Premium Projection Chart */}
            <div>
              <h4 className="text-lg font-bold text-black mb-4">Premium Projection Comparison</h4>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={analysisResults}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="year" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Line type="monotone" dataKey="basePremium" stroke="#2563eb" strokeWidth={2} name="Current Premium" />
                  <Line type="monotone" dataKey="modifiedPremium" stroke="#dc2626" strokeWidth={2} name="Modified Premium" />
                </LineChart>
              </ResponsiveContainer>
            </div>

            {/* Cash Value Projection Chart */}
            <div>
              <h4 className="text-lg font-bold text-black mb-4">Cash Value Projection Comparison</h4>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={analysisResults}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="year" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Line type="monotone" dataKey="baseCashValue" stroke="#16a34a" strokeWidth={2} name="Current Cash Value" />
                  <Line type="monotone" dataKey="modifiedCashValue" stroke="#ea580c" strokeWidth={2} name="Modified Cash Value" />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </div>

          {/* Analysis Summary */}
          <div className="mt-8 bg-white p-6 rounded-lg shadow-md">
            <h4 className="text-lg font-bold text-black mb-4">Analysis Summary</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {(formData.currentPremium || extractedCustomerInfo?.annualPremium) && (
                <div className="text-black">
                  <strong>Current Annual Premium:</strong> ${formData.currentPremium || extractedCustomerInfo?.annualPremium}
                </div>
              )}
              {formData.changeToNewAmount && formData.newPremiumAmount && (
                <div className="text-black">
                  <strong>New Premium Amount:</strong> ${formData.newPremiumAmount}
                </div>
              )}
              {formData.modifyPremiumStartingAge && formData.newPremiumAmount && formData.modifyPremiumAge && (
                <div className="text-black">
                  <strong>Modified Premium:</strong> ${formData.newPremiumAmount} starting from age {formData.modifyPremiumAge}
                </div>
              )}
              {formData.lumpSumPremium && formData.lumpSumAmount && (
                <div className="text-black">
                  <strong>Lump Sum Premium:</strong> ${formData.lumpSumAmount}
                </div>
              )}
              {formData.stopPremiumNow && (
                <div className="text-black">
                  <strong>Stop Premium:</strong> Now (Age {formData.stopPremiumAge || extractedCustomerInfo?.currentAge || '40'})
                </div>
              )}
              {formData.modifyStopPremium && formData.modifyStopPremiumAge && (
                <div className="text-black">
                  <strong>Stop Premium at Age:</strong> {formData.modifyStopPremiumAge}
                </div>
              )}
              {formData.cashValueTarget && (
                <div className="text-black">
                  <strong>Cash Value Target:</strong> ${formData.cashValueTarget}
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Comprehensive Report */}
      {showReport && (
        <div className="mt-8 bg-gradient-to-r from-blue-50 to-blue-100 p-6 rounded-xl shadow-lg border-l-4 border-blue-500">
          <h3 className="text-xl font-bold text-blue-800 mb-6 text-center">📈 Comprehensive Premium Analysis Report</h3>
          
          <div className="space-y-6">
            {/* Report Header */}
            <div className="bg-white p-4 rounded-lg shadow-sm">
              <h4 className="text-lg font-bold text-black mb-3">Report Details</h4>
              <p><strong>Report Generated:</strong> {new Date().toLocaleString()}</p>
              <p><strong>Customer:</strong> {selectedCustomerData?.name || 'N/A'}</p>
              <p><strong>Policy Number:</strong> {selectedCustomerData?.details?.["Policy Number"] || selectedCustomerData?.policyNumber || 'N/A'}</p>
              <p><strong>Policy Type:</strong> {selectedPolicyData?.name || 'N/A'}</p>
            </div>

            {/* Executive Summary */}
            <div className="bg-white p-4 rounded-lg shadow-sm">
              <h4 className="text-lg font-bold text-black mb-3">Executive Summary</h4>
              <ul className="space-y-2 text-black">
                {(formData.currentPremium || extractedCustomerInfo?.annualPremium) && <li>• Current annual premium: ${formData.currentPremium || extractedCustomerInfo?.annualPremium}</li>}
                {formData.changeToNewAmount && formData.newPremiumAmount && <li>• New premium amount: ${formData.newPremiumAmount}</li>}
                {formData.modifyPremiumStartingAge && formData.newPremiumAmount && formData.modifyPremiumAge && <li>• Modified premium: ${formData.newPremiumAmount} starting from age ${formData.modifyPremiumAge}</li>}
                {formData.lumpSumPremium && formData.lumpSumAmount && <li>• Lump sum premium: ${formData.lumpSumAmount}</li>}
                {formData.stopPremiumNow && <li>• Analysis includes immediate premium payment cessation</li>}
                {formData.modifyStopPremium && formData.modifyStopPremiumAge && <li>• Analysis includes premium payment cessation from age ${formData.modifyStopPremiumAge}</li>}
                {formData.cashValueTarget && <li>• Cash value target: ${formData.cashValueTarget}</li>}
              </ul>
            </div>

            {/* Recommendations */}
            <div className="bg-white p-4 rounded-lg shadow-sm">
              <h4 className="text-lg font-bold text-black mb-3">Recommendations</h4>
              <ul className="space-y-2 text-black">
                {(formData.stopPremiumNow || formData.modifyStopPremium) && <li>• Consider the policy risk when stopping premium payments</li>}
                {formData.lumpSumPremium && <li>• Lump sum option eliminates future premium payment uncertainty</li>}
                {(formData.changeToNewAmount || formData.modifyPremiumStartingAge) && <li>• Premium adjustments can help optimize policy performance</li>}
                {formData.cashValueTarget && <li>• Setting cash value targets helps achieve financial goals</li>}
                <li>• Regular policy reviews are recommended to monitor performance</li>
                <li>• Consider current interest rate environment when making premium decisions</li>
              </ul>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PremiumAnalysisForm;