# Illustration Type Filtering Implementation

## Overview
This implementation adds backend filtering logic to the Policy Selection page's "Go to Illustration" button. When clicked, it triggers a backend API call to determine which illustration types should be displayed based on the selected policy and customer data.

## Implementation Details

### 1. Illustration Type IDs
Each illustration type now has a unique ID from 1 to 6:
- **As-Is** → ID: 1
- **Face Amount** → ID: 2  
- **Premium** → ID: 3
- **Interest Rate** → ID: 4
- **Income (<PERSON><PERSON> & Withdrawal)** → ID: 5
- **Loan Repayment** → ID: 6

### 2. Backend API Integration

#### API Endpoint
- **URL**: `POST /api/illustrations/allowed-types`
- **Request Body**:
```json
{
  "policyId": 12345,
  "customerId": 67890,
  "policyType": "Whole Life"
}
```

#### Expected Response
```json
{
  "success": true,
  "allowedTypeIds": [1, 2, 3, 4],
  "message": "Illustration types filtered successfully"
}
```

### 3. Frontend Flow

1. **User clicks "Go to Illustration"** in PolicySelection component
2. **Frontend calls backend API** with policy and customer data
3. **Backend returns allowed illustration type IDs**
4. **Frontend stores allowed types** in DashboardContext state
5. **IllustrationMainPage filters and displays** only allowed types
6. **Hidden illustration types** are not rendered in the UI

### 4. Key Files Modified

#### `src/services/illustrationService.ts`
- New service for API calls and filtering logic
- `fetchAllowedIllustrationTypes()` function
- Helper functions for filtering illustration tabs

#### `src/contexts/DashboardContext.tsx`
- Added `allowedIllustrationTypes` state
- Added `fetchAllowedIllustrationTypes()` function
- State management for illustration type filtering

#### `src/types/index.ts`
- Updated `DashboardState` interface
- Added `allowedIllustrationTypes: number[]` field

#### `src/components/dashboard/PolicySelection.tsx`
- Modified "Go to Illustration" button click handler
- Added backend API call before navigation
- Added loading state and error handling

#### `src/components/dashboard/IllustrationMainPage.tsx`
- Added filtering logic using `useMemo`
- Visual indicators for filtered types
- Error handling display

#### `src/components/dashboard/IllustrationNavigation.tsx`
- Updated to use filtered illustration tabs
- Consistent filtering across navigation components

### 5. Error Handling

- **API Failure**: Falls back to showing all illustration types
- **Network Issues**: Graceful degradation with user notification
- **Invalid Response**: Validates response format and type IDs
- **Loading States**: Shows loading indicators during API calls

### 6. Visual Feedback

- **Loading State**: Button shows "Loading Illustration Types..." with spinner
- **Filtered Types**: Blue info card shows "X of Y illustration types"
- **API Errors**: Yellow warning card with error message
- **Seamless UX**: User can still proceed even if API fails

### 7. Testing the Implementation

To test the filtering functionality:

1. **Mock Backend Response**: Use `mockFetchAllowedIllustrationTypes()` in development
2. **Modify Mock Logic**: Edit the mock function to return different type IDs
3. **Verify Filtering**: Check that only allowed types appear in the UI
4. **Test Error Cases**: Simulate API failures to verify fallback behavior

### 8. Backend Requirements (Frontend-Only Implementation)

The backend should implement the `/api/illustrations/allowed-types` endpoint that:
- Accepts policy and customer data
- Applies business logic to determine allowed illustration types
- Returns array of type IDs (1-6) based on policy rules
- Handles errors gracefully with appropriate HTTP status codes

### 9. Configuration

The API base URL is configurable via environment variable:
```
VITE_API_BASE_URL=http://localhost:8000
```

### 10. Future Enhancements

- **Caching**: Cache allowed types to avoid repeated API calls
- **Real-time Updates**: Refresh allowed types when policy data changes
- **Admin Override**: Allow administrators to override filtering rules
- **Analytics**: Track which illustration types are most commonly filtered
